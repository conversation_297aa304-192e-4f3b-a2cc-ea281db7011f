@import 'tailwindcss';
@import '@skeletonlabs/skeleton';
@import '@skeletonlabs/skeleton/optional/presets';

@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';
@source '../node_modules/@skeletonlabs/skeleton-svelte/dist';

@custom-variant dark (&:where([data-mode=dark], [data-mode=dark] *));
:root {
    --header-height: 48px;
    /*--footer-height: 48px;*/
    --footer-height: 0px;
}

html,
body {
    @apply h-full;
    margin: 0;
}

.header-height {
    height: var(--header-height);
}

.container-content-height {
    height: calc(100vh - (var(--header-height) + var(--footer-height)));
}


[data-theme='agora'] {
    --text-scaling: 1.067;
    --base-font-color: var(--color-surface-950);
    --base-font-color-dark: var(--color-surface-50);
    --base-font-family: Avenir, Montserrat, Corbel, 'URW Gothic', source-sans-pro, sans-serif;
    --base-font-size: 18px;
    --base-line-height: 28px;
    --base-font-weight: normal;
    --base-font-style: normal;
    --base-letter-spacing: 0em;
    --heading-font-color: inherit;
    --heading-font-color-dark: inherit;
    --heading-font-family: Avenir, Montserrat, Corbel, 'URW Gothic', source-sans-pro, sans-serif;
    --heading-font-weight: bolder;
    --heading-font-style: normal;
    --heading-letter-spacing: inherit;
    --anchor-font-color: var(--color-primary-600);
    --anchor-font-color-dark: var(--color-primary-500);
    --anchor-font-family: inherit;
    --anchor-font-size: inherit;
    --anchor-line-height: inherit;
    --anchor-font-weight: inherit;
    --anchor-font-style: inherit;
    --anchor-letter-spacing: inherit;
    --anchor-text-decoration: none;
    --anchor-text-decoration-hover: underline;
    --anchor-text-decoration-active: none;
    --anchor-text-decoration-focus: none;
    --spacing: 0.22rem;
    --radius-base: 0.375rem;
    --radius-container: 0.75rem;
    --default-border-width: 1px;
    --default-divide-width: 1px;
    --default-ring-width: 1px;
    --body-background-color: var(--color-surface-50);
    --body-background-color-dark: var(--color-surface-950);
    --color-primary-50: oklch(89.16% 0.06 30.31deg);
    --color-primary-100: oklch(80.32% 0.1 19.47deg);
    --color-primary-200: oklch(72.11% 0.14 16.54deg);
    --color-primary-300: oklch(64.85% 0.19 17.2deg);
    --color-primary-400: oklch(59.52% 0.22 20.26deg);
    --color-primary-500: oklch(56.35% 0.23 24.98deg);
    --color-primary-600: oklch(53.05% 0.22 25.05deg);
    --color-primary-700: oklch(49.93% 0.2 25.02deg);
    --color-primary-800: oklch(46.52% 0.19 25.13deg);
    --color-primary-900: oklch(43.31% 0.17 25.13deg);
    --color-primary-950: oklch(39.78% 0.16 25.29deg);
    --color-primary-contrast-dark: var(--color-primary-950);
    --color-primary-contrast-light: var(--color-primary-50);
    --color-primary-contrast-50: var(--color-primary-contrast-dark);
    --color-primary-contrast-100: var(--color-primary-contrast-dark);
    --color-primary-contrast-200: var(--color-primary-contrast-dark);
    --color-primary-contrast-300: var(--color-primary-contrast-dark);
    --color-primary-contrast-400: var(--color-primary-contrast-light);
    --color-primary-contrast-500: var(--color-primary-contrast-light);
    --color-primary-contrast-600: var(--color-primary-contrast-light);
    --color-primary-contrast-700: var(--color-primary-contrast-light);
    --color-primary-contrast-800: var(--color-primary-contrast-light);
    --color-primary-contrast-900: var(--color-primary-contrast-light);
    --color-primary-contrast-950: var(--color-primary-contrast-light);
    --color-secondary-50: oklch(96.01% 0.02 21.44deg);
    --color-secondary-100: oklch(94.76% 0.02 20.93deg);
    --color-secondary-200: oklch(93.31% 0.03 17.72deg);
    --color-secondary-300: oklch(92.03% 0.03 20.23deg);
    --color-secondary-400: oklch(90.58% 0.04 17.88deg);
    --color-secondary-500: oklch(89.32% 0.04 17.95deg);
    --color-secondary-600: oklch(81.17% 0.04 12.42deg);
    --color-secondary-700: oklch(72.79% 0.04 9.14deg);
    --color-secondary-800: oklch(64.01% 0.05 3.25deg);
    --color-secondary-900: oklch(55.13% 0.05 0.94deg);
    --color-secondary-950: oklch(45.98% 0.06 357.61deg);
    --color-secondary-contrast-dark: var(--color-secondary-950);
    --color-secondary-contrast-light: var(--color-secondary-50);
    --color-secondary-contrast-50: var(--color-secondary-contrast-dark);
    --color-secondary-contrast-100: var(--color-secondary-contrast-dark);
    --color-secondary-contrast-200: var(--color-secondary-contrast-dark);
    --color-secondary-contrast-300: var(--color-secondary-contrast-dark);
    --color-secondary-contrast-400: var(--color-secondary-contrast-dark);
    --color-secondary-contrast-500: var(--color-secondary-contrast-dark);
    --color-secondary-contrast-600: var(--color-secondary-contrast-dark);
    --color-secondary-contrast-700: var(--color-secondary-contrast-dark);
    --color-secondary-contrast-800: var(--color-secondary-contrast-light);
    --color-secondary-contrast-900: var(--color-secondary-contrast-light);
    --color-secondary-contrast-950: var(--color-secondary-contrast-light);
    --color-tertiary-50: oklch(92.66% 0.02 162.81deg);
    --color-tertiary-100: oklch(83.28% 0.02 162.87deg);
    --color-tertiary-200: oklch(73.85% 0.03 161.62deg);
    --color-tertiary-300: oklch(63.9% 0.03 161.55deg);
    --color-tertiary-400: oklch(53.81% 0.04 160.33deg);
    --color-tertiary-500: oklch(43.03% 0.04 159.72deg);
    --color-tertiary-600: oklch(40.06% 0.04 159.46deg);
    --color-tertiary-700: oklch(37.05% 0.03 159.06deg);
    --color-tertiary-800: oklch(33.73% 0.02 163.26deg);
    --color-tertiary-900: oklch(30.6% 0.02 163.62deg);
    --color-tertiary-950: oklch(27.39% 0.01 164.08deg);
    --color-tertiary-contrast-dark: var(--color-tertiary-950);
    --color-tertiary-contrast-light: var(--color-tertiary-50);
    --color-tertiary-contrast-50: var(--color-tertiary-contrast-dark);
    --color-tertiary-contrast-100: var(--color-tertiary-contrast-dark);
    --color-tertiary-contrast-200: var(--color-tertiary-contrast-dark);
    --color-tertiary-contrast-300: var(--color-tertiary-contrast-dark);
    --color-tertiary-contrast-400: var(--color-tertiary-contrast-light);
    --color-tertiary-contrast-500: var(--color-tertiary-contrast-light);
    --color-tertiary-contrast-600: var(--color-tertiary-contrast-light);
    --color-tertiary-contrast-700: var(--color-tertiary-contrast-light);
    --color-tertiary-contrast-800: var(--color-tertiary-contrast-light);
    --color-tertiary-contrast-900: var(--color-tertiary-contrast-light);
    --color-tertiary-contrast-950: var(--color-tertiary-contrast-light);
    --color-success-50: oklch(96.13% 0.03 196.6deg);
    --color-success-100: oklch(89.16% 0.04 198.67deg);
    --color-success-200: oklch(82.03% 0.04 200.1deg);
    --color-success-300: oklch(75.02% 0.05 197.81deg);
    --color-success-400: oklch(67.65% 0.06 198.95deg);
    --color-success-500: oklch(60.21% 0.06 199.99deg);
    --color-success-600: oklch(53.37% 0.06 200.31deg);
    --color-success-700: oklch(46.07% 0.05 200.91deg);
    --color-success-800: oklch(38.74% 0.05 201.47deg);
    --color-success-900: oklch(30.8% 0.04 202.62deg);
    --color-success-950: oklch(22.58% 0.04 203.51deg);
    --color-success-contrast-dark: var(--color-success-950);
    --color-success-contrast-light: var(--color-success-50);
    --color-success-contrast-50: var(--color-success-contrast-dark);
    --color-success-contrast-100: var(--color-success-contrast-dark);
    --color-success-contrast-200: var(--color-success-contrast-dark);
    --color-success-contrast-300: var(--color-success-contrast-dark);
    --color-success-contrast-400: var(--color-success-contrast-dark);
    --color-success-contrast-500: var(--color-success-contrast-dark);
    --color-success-contrast-600: var(--color-success-contrast-light);
    --color-success-contrast-700: var(--color-success-contrast-light);
    --color-success-contrast-800: var(--color-success-contrast-light);
    --color-success-contrast-900: var(--color-success-contrast-light);
    --color-success-contrast-950: var(--color-success-contrast-light);
    --color-warning-50: oklch(99.02% 0.05 107.26deg);
    --color-warning-100: oklch(97.17% 0.08 106.9deg);
    --color-warning-200: oklch(95.37% 0.1 106.93deg);
    --color-warning-300: oklch(93.92% 0.12 107.05deg);
    --color-warning-400: oklch(92.23% 0.14 107.19deg);
    --color-warning-500: oklch(90.62% 0.16 107.27deg);
    --color-warning-600: oklch(82.89% 0.15 105.38deg);
    --color-warning-700: oklch(74.98% 0.14 103.08deg);
    --color-warning-800: oklch(66.88% 0.13 100.09deg);
    --color-warning-900: oklch(58.57% 0.11 96.14deg);
    --color-warning-950: oklch(50% 0.1 90.39deg);
    --color-warning-contrast-dark: var(--color-warning-950);
    --color-warning-contrast-light: var(--color-warning-50);
    --color-warning-contrast-50: var(--color-warning-contrast-dark);
    --color-warning-contrast-100: var(--color-warning-contrast-dark);
    --color-warning-contrast-200: var(--color-warning-contrast-dark);
    --color-warning-contrast-300: var(--color-warning-contrast-dark);
    --color-warning-contrast-400: var(--color-warning-contrast-dark);
    --color-warning-contrast-500: var(--color-warning-contrast-dark);
    --color-warning-contrast-600: var(--color-warning-contrast-dark);
    --color-warning-contrast-700: var(--color-warning-contrast-dark);
    --color-warning-contrast-800: var(--color-warning-contrast-light);
    --color-warning-contrast-900: var(--color-warning-contrast-light);
    --color-warning-contrast-950: var(--color-warning-contrast-light);
    --color-error-50: oklch(92.15% 0.04 17.94deg);
    --color-error-100: oklch(88.79% 0.06 17.06deg);
    --color-error-200: oklch(85.66% 0.08 18.81deg);
    --color-error-300: oklch(82.49% 0.1 18.61deg);
    --color-error-400: oklch(79.59% 0.12 20.01deg);
    --color-error-500: oklch(76.7% 0.14 20.25deg);
    --color-error-600: oklch(69.93% 0.13 22.17deg);
    --color-error-700: oklch(63.19% 0.13 24.3deg);
    --color-error-800: oklch(55.97% 0.12 26.75deg);
    --color-error-900: oklch(48.9% 0.11 29.49deg);
    --color-error-950: oklch(41.46% 0.11 32.39deg);
    --color-error-contrast-dark: var(--color-error-950);
    --color-error-contrast-light: var(--color-error-50);
    --color-error-contrast-50: var(--color-error-contrast-dark);
    --color-error-contrast-100: var(--color-error-contrast-dark);
    --color-error-contrast-200: var(--color-error-contrast-dark);
    --color-error-contrast-300: var(--color-error-contrast-dark);
    --color-error-contrast-400: var(--color-error-contrast-dark);
    --color-error-contrast-500: var(--color-error-contrast-dark);
    --color-error-contrast-600: var(--color-error-contrast-dark);
    --color-error-contrast-700: var(--color-error-contrast-light);
    --color-error-contrast-800: var(--color-error-contrast-light);
    --color-error-contrast-900: var(--color-error-contrast-light);
    --color-error-contrast-950: var(--color-error-contrast-light);
    --color-surface-50: oklch(97.61% 0 none);
    --color-surface-100: oklch(90.06% 0 none);
    --color-surface-200: oklch(82.34% 0 none);
    --color-surface-300: oklch(74.44% 0 none);
    --color-surface-400: oklch(66.33% 0 none);
    --color-surface-500: oklch(57.95% 0 none);
    --color-surface-600: oklch(49.62% 0 none);
    --color-surface-700: oklch(40.91% 0 none);
    --color-surface-800: oklch(32.11% 0 none);
    --color-surface-900: oklch(22.21% 0 none);
    --color-surface-950: oklch(9.69% 0 none);
    --color-surface-contrast-dark: var(--color-surface-950);
    --color-surface-contrast-light: var(--color-surface-50);
    --color-surface-contrast-50: var(--color-surface-contrast-dark);
    --color-surface-contrast-100: var(--color-surface-contrast-dark);
    --color-surface-contrast-200: var(--color-surface-contrast-dark);
    --color-surface-contrast-300: var(--color-surface-contrast-dark);
    --color-surface-contrast-400: var(--color-surface-contrast-dark);
    --color-surface-contrast-500: var(--color-surface-contrast-dark);
    --color-surface-contrast-600: var(--color-surface-contrast-light);
    --color-surface-contrast-700: var(--color-surface-contrast-light);
    --color-surface-contrast-800: var(--color-surface-contrast-light);
    --color-surface-contrast-900: var(--color-surface-contrast-light);
    --color-surface-contrast-950: var(--color-surface-contrast-light);
}

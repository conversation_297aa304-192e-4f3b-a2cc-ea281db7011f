import seedrandom from 'seedrandom';

const DEFAULT_SEED = 'my-seed';
const DEFAULT_TIMEFRAME = 86400; // 1 day in seconds

type Candle = {
    time: number;  // Unix timestamp or ISO string
    open: number;
    high: number;
    low: number;
    close: number;
};

type LinePoint = {
    time: number;
    value: number;
};

function getRandomInRange(rng: () => number, min: number, max: number) {
    return +(rng() * (max - min) + min).toFixed(2);
}

export function generateCandles(count: number, timeFrame: number = DEFAULT_TIMEFRAME, seed: string = DEFAULT_SEED): Candle[] {
    const candles: Candle[] = [];
    let timestamp = Math.floor(new Date('2023-01-01').getTime() / 1000);
    let lastClose = 105;
    const rng = seedrandom(seed);

    for (let i = 0; i < count; i++) {
        const open = lastClose;
        const high = open + rng() * 10;
        const low = open - rng() * 10;
        const close = low + rng() * (high - low);

        candles.push({
            time: timestamp,
            open: parseFloat(open.toFixed(2)),
            high: parseFloat(high.toFixed(2)),
            low: parseFloat(low.toFixed(2)),
            close: parseFloat(close.toFixed(2)),
        });

        lastClose = close;
        timestamp += timeFrame;
    }

    return candles;
}

export function generateTradesFromCandles(candles: Candle[], tradeCount: number, seed: string = DEFAULT_SEED) {
    const trades = [];

    const rng = seedrandom(seed);
    for (let i = 0; i < tradeCount; i++) {
        const entryIndex = Math.floor(rng() * (candles.length - 2));
        const exitIndex = Math.floor(rng() * (candles.length - entryIndex - 1)) + entryIndex + 1;

        const entryCandle = candles[entryIndex];
        const exitCandle = candles[exitIndex];

        const entryPrice = getRandomInRange(rng, entryCandle.low, entryCandle.high);
        const exitPrice = getRandomInRange(rng, exitCandle.low, exitCandle.high);

        trades.push({
            tradeId: i + 1,
            entryTime: entryCandle.time,
            exitTime: exitCandle.time,
            entryPrice,
            exitPrice,
            entrySide: "long"
        });
    }

    return trades;
}

export function generateLineData(candles: Candle[], accessor: (keyof Candle) = 'close'): LinePoint[] {
    return candles.map((candle) => {
        return {
            time: candle.time,
            value: candle[accessor],
            color: candle.close > candle.open ? 'green' : 'red',
        }
    });
}
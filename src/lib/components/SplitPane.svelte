<script lang="ts">
    import {onMount, type Snippet} from 'svelte';

    export type Length = `${'+' | '-' | ''}${number}${'px' | '%'}`;
    export type Orientation = 'horizontal' | 'vertical';
    export type Extension = {
        pos: Length;
        max: Length;
        min: Length;
        setPos: (p: Length) => void
    };

    interface Panel {
        peek: Length;
        snap: Length;
        min: Length;
    }

    interface Props {
        id?: string | undefined;
        type: Orientation;
        pos?: Length;
        min?: Length;
        max?: Length;
        gap?: Length;
        snapThreshold?: Length;
        disabled?: boolean;
        a?: Snippet<[Extension]>;
        b?: Snippet<[Extension]>;
    }

    let panelA: Panel = {
        peek: "0px",
        snap: "0px",
        min: "0px",
    };

    let panelB: Panel = {
        peek: "50px",
        snap: "50px",
        min: "20%",
    };

    let {
        id = undefined,
        type,
        pos = $bindable('50%'),
        gap = '2px',
        disabled = false,
        a,
        b
    }: Props = $props();

    let contentRect: DOMRectReadOnly | undefined = $state(undefined);
    let dragging = $state(false);

    let cachedPxValues = $state({
        snapAPx: 0,
        snapBPx: 0,
        peekAPx: 0,
        peekBPx: 0,
        minAPx: 0,
        minBPx: 0,
        gapPx: 0,
        containerLength: 0
    });

    let _loc: Length = $state(pos);
    let _min: Length = $state(panelA.min);
    let _max: Length = $state(panelB.min);

    let snapState = $state<'normal' | 'peek-a' | 'peek-b'>('normal');
    let dragPosition = $state(pos);

    $effect(() => {
        if (!contentRect) return;

        const {width, height} = contentRect;
        updateCachedValues(type === 'horizontal' ? width : height);
    });

    function updateCachedValues(length: number) {
        if (cachedPxValues.containerLength === length) return;

        cachedPxValues = {
            snapAPx: lengthToPx(panelA.snap, length),
            snapBPx: lengthToPx(panelB.snap, length),
            peekAPx: lengthToPx(panelA.peek, length),
            peekBPx: lengthToPx(panelB.peek, length),
            minAPx: lengthToPx(panelA.min, length),
            minBPx: lengthToPx(panelB.min, length),
            gapPx: lengthToPx(gap, length),
            containerLength: length
        };
        console.log('updateCachedValues', cachedPxValues);
    }

    function setPosition(position: Length) {
        const containerLength = type === 'horizontal' ? contentRect?.width : contentRect?.height;
        if (!containerLength) return;
        
        const positionPx = lengthToPx(position, containerLength);
        const snapThresholdPx = lengthToPx('10px', containerLength);
        const peekAPx = lengthToPx(panelA.peek, containerLength);
        const peekBPx = lengthToPx(panelB.peek, containerLength);
        
        // Determine snap state based on position
        if (peekAPx > 0 && positionPx < snapThresholdPx) {
            snapState = 'peek-a';
        } else if (peekBPx > 0 && positionPx > containerLength - snapThresholdPx) {
            snapState = 'peek-b';
        } else {
            snapState = 'normal';
            dragPosition = position;
        }
    }

    function normalize(length: string) {
        if (length[0] === '-') {
            return `calc(100% - ${length.slice(1)})`;
        }

        return length;
    }

    function lengthToPx(value: Length, containerSize: number): number {
        let numValue = parseFloat(value);

        if (value.includes('%')) {
            numValue = (numValue / 100) * containerSize;

            // } else if (value.includes('em')) {
            //     // Get computed font size of container element
            //     const fontSize = parseFloat(getComputedStyle(container).fontSize);
            //     return numValue * fontSize;
            // } else if (value.includes('rem')) {
            //     // Get root font size
            //     const rootFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
            //     return numValue * rootFontSize;
        }

        // Fallback: assume pixels
        return numValue;
    }

    function update(x: number, y: number) {
        if (disabled || !contentRect) return;

        const {top, left, width, height} = contentRect
        const [length, offset, position] = type === 'horizontal'
            ? [width, left, x]
            : [height, top, y];

        setPosition(`${Math.min(length, Math.max(0, position - offset))}px`, length);

    }

    function save() {
        if (!id) return;
        // localStorage.setItem(`split-pane-${id}`, pos);
    }

    function load() {
        if (!id) return;
        const storedPos = localStorage.getItem(`split-pane-${id}`);
        if (storedPos) {
            pos = storedPos;
        }
    }

    function drag(node: HTMLElement, callback: (event: PointerEvent) => void) {
        const pointerdown = (event: PointerEvent) => {
            if (
                (event.pointerType === 'mouse' && event.button === 2) ||
                (event.pointerType !== 'mouse' && !event.isPrimary)
            ) {
                return;
            }

            node.setPointerCapture(event.pointerId);

            event.preventDefault();

            dragging = true;

            const onpointerup = () => {
                dragging = false;

                node.setPointerCapture(event.pointerId);

                window.removeEventListener('pointermove', callback, false);
                window.removeEventListener('pointerup', onpointerup, false);
                save();
            };

            window.addEventListener('pointermove', callback, false);
            window.addEventListener('pointerup', onpointerup, false);
        };

        $effect(() => {
            node.addEventListener('pointerdown', pointerdown, {capture: true, passive: false});

            return () => {
                node.removeEventListener('pointerdown', pointerdown);
            };
        });
    }

    function extension(side: 'a' | 'b'): Extension {
        const {containerLength} = cachedPxValues;
        return {
            pos: _loc,
            maximize: () => {
                setPosition(side === 'a' ? '100%' : '-100%', containerLength);
            },
            setPos: (p: Length) => {
                pos = side === 'a' ? p : `calc(100% - ${p})`;
                save();
            },
        };
    }
</script>


<svelte-split-pane
    bind:contentRect={contentRect}
    data-pane={id}
    data-orientation={type}
    data-snap-state={snapState}
    style="
        --gap: {gap}; 
        --pos: {snapState === 'normal' ? normalize(dragPosition) : '0px'};
        --min: {normalize(panelA.min)};
        --max: {normalize(panelB.min)};
        --peek-a: {normalize(panelA.peek)};
        --peek-b: {normalize(panelB.peek)};
    "
>
    {#if contentRect}
        <svelte-split-pane-section>
            {@render a?.(extension('a'))}
        </svelte-split-pane-section>
        <svelte-split-pane-divider class:disabled use:drag={(e) => update(e.clientX, e.clientY)}
        ></svelte-split-pane-divider>
        <svelte-split-pane-section>
            {@render b?.(extension('b'))}
        </svelte-split-pane-section>
    {/if}
</svelte-split-pane>

{#if dragging}
    <svelte-split-pane-mousecatcher></svelte-split-pane-mousecatcher>
{/if}

<style>
    svelte-split-pane {
        --sp-thickness: var(--thickness, 8px);
        --sp-color: var(--color, transparent);
        display: grid;
        position: relative;
        width: 100%;
        height: 100%;
        gap: var(--gap, 0px);
    }

    svelte-split-pane[data-orientation='horizontal'] {
        grid-template-columns: 
            clamp(var(--min), var(--pos), var(--max)) 1fr;
    }

    svelte-split-pane[data-orientation='horizontal'][data-snap-state='peek-a'] {
        grid-template-columns: var(--peek-a) 1fr;
    }

    svelte-split-pane[data-orientation='horizontal'][data-snap-state='peek-b'] {
        grid-template-columns: calc(100% - var(--peek-b)) 1fr;
    }

    svelte-split-pane[data-orientation='vertical'] {
        grid-template-rows: 
            clamp(var(--min), var(--pos), var(--max)) 1fr;
    }

    svelte-split-pane[data-orientation='vertical'][data-snap-state='peek-a'] {
        grid-template-rows: var(--peek-a) 1fr;
    }

    svelte-split-pane[data-orientation='vertical'][data-snap-state='peek-b'] {
        grid-template-rows: calc(100% - var(--peek-b)) 1fr;
    }

    svelte-split-pane-section {
        width: 100%;
        height: 100%;
        overflow: auto;
    }

    svelte-split-pane-section > :global(*) {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    svelte-split-pane-mousecatcher {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.0001);
    }

    svelte-split-pane-divider {
        position: absolute;
        touch-action: none !important;
    }

    svelte-split-pane-divider::after {
        content: '';
        position: absolute;
        background-color: var(--sp-color);
    }

    [data-orientation='horizontal'] > svelte-split-pane-divider {
        padding: 0 calc(0.5 * var(--sp-thickness));
        width: 0;
        height: 100%;
        cursor: ew-resize;
        left: clamp(var(--min), calc(var(--pos) + var(--gap) * 0.5), var(--max));
        transform: translate(calc(-0.5 * var(--sp-thickness)), 0);
    }

    [data-orientation='horizontal'] > svelte-split-pane-divider.disabled {
        cursor: default;
    }

    [data-orientation='horizontal'] > svelte-split-pane-divider::after {
        left: 50%;
        top: 0;
        width: 1px;
        height: 100%;
    }

    [data-orientation='vertical'] > svelte-split-pane-divider {
        padding: calc(0.5 * var(--sp-thickness)) 0;
        width: 100%;
        height: 0;
        cursor: ns-resize;
        top: clamp(var(--min), calc(var(--pos) + var(--gap) * 0.5), var(--max));
        transform: translate(0, calc(-0.5 * var(--sp-thickness)));
    }

    [data-orientation='vertical'] > svelte-split-pane-divider.disabled {
        cursor: default;
    }

    [data-orientation='vertical'] > svelte-split-pane-divider::after {
        top: 50%;
        left: 0;
        width: 100%;
        height: 1px;
        padding-left: 10px;
    }
</style>
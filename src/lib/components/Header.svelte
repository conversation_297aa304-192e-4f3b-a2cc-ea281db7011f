<script lang="ts">
    import {AppBar} from '@skeletonlabs/skeleton-svelte';
    import LightSwitch from '$lib/components/LightSwitch.svelte';
    // Remove the logo import since we'll use inline SVG
</script>

<AppBar
        classes="flex justify-center z-40 border border-surface-100-900/30 h-12"
        background="bg-surface-100-900/80 backdrop-blur-lg"
>
    <!--Lead-->
    {#snippet lead()}
        <div class="flex items-center gap-x-2">
            <a href="/" class="preset-gradient flex items-center gap-x-1 rounded px-1.5">
                <span class="font-bold uppercase">
                    <!-- Inline SVG instead of img tag -->
                    <svg
                        viewBox="8 8 118 45"
                        role="img"
                        class="h-10"
                        xmlns="http://www.w3.org/2000/svg">
                        <style>
                            .text { font: 400 24px 'Segoe UI', sans-serif; fill: currentColor; }
                            .candle { fill: currentColor; }
                            .wick { stroke: currentColor; stroke-width: 1; }
                        </style>
                        <g transform="translate(-2.7072758,0.45121263)">
                            <g class="candle" transform="translate(10,10)" style="display:inline">
                                <!-- First candle -->
                                <line class="wick" x1="3.5" y1="0" x2="3.5" y2="10" />
                                <rect x="0" y="10" width="6" height="20" />
                                <line class="wick" x1="3.5" y1="30" x2="3.5" y2="36" />
                                <!-- Second candle -->
                                <line class="wick" x1="13" y1="0" x2="13" y2="5" />
                                <rect x="10" y="5" width="6" height="30" />
                                <line class="wick" x1="13" y1="35" x2="13" y2="40" />
                                <!-- Third candle -->
                                <line class="wick" x1="23" y1="0" x2="23" y2="12" />
                                <rect x="20" y="12" width="6" height="18" />
                                <line class="wick" x1="23" y1="30" x2="23" y2="36" />
                            </g>
                            <text
                                class="text"
                                x="40"
                                y="38"
                                style="font-style:normal;font-variant:normal;font-weight:200;font-stretch:normal;font-size:24px;line-height:normal;font-family:'Segoe UI', sans-serif">
                                Kiaros
                            </text>
                        </g>
                    </svg>
                </span>
            </a>
            <nav class="btn-group p-2 hidden sm:flex">
                <a href="/dashboard" class="btn hover:preset-tonal">Dashboard</a>
                <a href="/" class="btn hover:preset-tonal">Backtest</a>
            </nav>
        </div>
    {/snippet}

    <!--Trail-->
    {#snippet trail()}
        <div class="flex items-center gap-x-2">
            <LightSwitch/>
        </div>
    {/snippet}

</AppBar>

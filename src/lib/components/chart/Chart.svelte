<script lang="ts">
    import {onMount} from 'svelte';
    import {themeMode} from '$lib/stores/theme';
    import {
        createChart,
        CandlestickSeries,
        HistogramSeries,
        LineSeries,
        type IChartApi
    } from 'lightweight-charts';
    import {generateCandles, generateLineData, generateTradesFromCandles} from "$lib/utils/mock-data";
    import {TradesIndicator} from "$lib/components/chart/plugins/krs-trades-series/trades-indicator";

    let container: HTMLElement;
    let chart: IChartApi;
    let tooltip: HTMLElement;

    const theme: Record<string, any> = {
        dark: {
            textColor: '#fff',
            backgroundColor: 'transparent',
            gridColor: 'oklch(25% 0 none)',
            crosshairColor: 'oklch(40% 0 none)',
            borderColor: 'oklch(22.21% 0 none)',
        },
        light: {
            textColor: '#000',
            backgroundColor: 'transparent',
            gridColor: 'oklch(90% 0 none)',
            crosshairColor: 'oklch(65% 0 none)',
            borderColor: 'oklch(82.34% 0 none)',
        },
    };

    $effect(() => {
        if (chart) {
            chart.remove();
        }
        chart = createChart(container, {
            autoSize: true,
            // handleScale: false,
            // handleScroll: false,
        });
        // chart.subscribeCrosshairMove((param) => {
        //     // console.log(param);
        //     if (param.point) {
        //         tooltip.style.display = 'block';
        //         tooltip.style.left = param.point.x + 'px';
        //         tooltip.style.top = param.point.y + 'px';
        //         tooltip.innerText = JSON.stringify(param.seriesData.entries().toArray()[0][1], null, 2);
        //
        //     } else {
        //         tooltip.style.display = 'none';
        //     }
        // });
    });

    $effect(() => {
        const {
            textColor,
            backgroundColor,
            crosshairColor,
            gridColor,
            borderColor,
        } = theme[$themeMode];

        chart.applyOptions(
            {
                layout: {
                    background: {
                        color: backgroundColor,
                    },
                    textColor: textColor,
                },
                crosshair: {
                    vertLine: {
                        color: crosshairColor,
                        labelBackgroundColor: textColor,
                    },
                    horzLine: {
                        color: crosshairColor,
                        labelBackgroundColor: textColor,
                    },
                },
                grid: {
                    vertLines: {
                        color: gridColor,
                    },
                    horzLines: {
                        color: gridColor,
                    },
                },
                timeScale: {
                    borderColor: borderColor,
                },
                overlayPriceScales: {
                    borderColor: borderColor,
                    textColor: textColor,

                },
                leftPriceScale: {
                    borderColor: borderColor,
                    textColor: textColor,
                },
                rightPriceScale: {
                    borderColor: borderColor,
                    textColor: textColor,
                },
            }
        );
    });

    let tradeData = [

        {
            tradeId: 2,
            entryTime: 1674086400,
            exitTime: 1676505600,
            exitPrice: 105,
            entryPrice: 92,
            entrySide: 'long'
        },
        {
            tradeId: 3,
            entryTime: 1675123200,
            exitTime: 1675728000,
            exitPrice: 102,
            entryPrice: 90,
            entrySide: 'long'
        },
        {
            tradeId: 1,
            entryTime: 1676246400,
            exitTime: 1676678400,
            exitPrice: 108,
            entryPrice: 110,
            entrySide: 'long'
        },
        {
            tradeId: 4,
            entryTime: 1673136000,
            exitTime: 1676419200,
            exitPrice: 115,
            entryPrice: 103,
            entrySide: 'long'
        },
        {
            tradeId: 5,
            entryTime: 1673395200,
            exitTime: 1674432000,
            exitPrice: 92,
            entryPrice: 99,
            entrySide: 'long'
        },

        {
            tradeId: 6,
            entryTime: 1673395200,
            exitTime: 1673827200,
            exitPrice: 100,
            entryPrice: 96,
            entrySide: 'long'
        },


    ]

    function renderChart() {
        const candleData = generateCandles(50);

        tradeData = generateTradesFromCandles(candleData, 10);

        const lineData = generateLineData(candleData, 'high');

        const candles = chart.addSeries(CandlestickSeries);
        const line = chart.addSeries(LineSeries);
        const volumeSeries = chart.addSeries(HistogramSeries, {
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: '',
        });

        const tradeIndicator = new TradesIndicator();
        tradeIndicator.setData(tradeData);
        candles.setData(candleData);
        candles.attachPrimitive(tradeIndicator);
        fitContent();
    }

    onMount(() => {
        renderChart();
    });

    function fitContent() {
        chart.timeScale().fitContent();
    }

</script>

<div bind:this={container} class="h-full w-full">
    <div class="absolute z-3 m-2">
    <pre class="absolute shadow-sm card p-4 bg-surface-200-800/65 backdrop-blur-2xl" bind:this={tooltip}>
    </pre>
        <div class="card backdrop-opacity-30 backdrop-blur-2xl p-2">
            <button type="button" class="btn preset-filled" onclick={fitContent}>Fit</button>
            <button type="button" class="btn preset-filled" onclick={fitContent}>Fit</button>
        </div>
    </div>
</div>

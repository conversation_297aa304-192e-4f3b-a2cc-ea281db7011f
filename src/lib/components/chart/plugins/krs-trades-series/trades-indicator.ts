import {CanvasRenderingTarget2D, type BitmapCoordinatesRenderingScope} from 'fancy-canvas';
import type {
    AutoscaleInfo,
    BarData,
    Coordinate,
    DataChangedScope,
    ISeriesPrimitive,
    IPrimitivePaneRenderer,
    IPrimitivePaneView,
    LineData,
    Logical,
    SeriesAttachedParameter,
    SeriesDataItemTypeMap,
    SeriesType,
    Time,
} from 'lightweight-charts';
import {PluginBase} from '../plugin-base';
import {cloneReadonly} from '../../helpers/simple-clone';
import {ClosestTimeIndexFinder} from '../../helpers/closest-index';
import {UpperLowerInRange} from '../../helpers/min-max-in-range';
import {positionsLine} from '../../helpers/dimensions/positions';
import {convertTime} from "$lib/components/chart/helpers/time";
import {
    MultiTouchChartEvents,
    type MultiTouchInteraction
} from "$lib/components/chart/plugins/delta-tooltip/multi-touch-chart-events";

export interface TradesIndicatorOptions {
    lineColor?: string;
    fillColor?: string;
    lineWidth?: number;
    showBranches?: boolean;
}

const defaults: Required<TradesIndicatorOptions> = {
    // lineColor: 'red',
    lineColor: 'rgb(25, 200, 100)',
    fillColor: 'rgba(25, 200, 100, 0.25)',
    lineWidth: 2,
    showBranches: true,
};

interface TradeData {
    entryTime: Time;
    exitTime: Time;
    entryPrice: number;
    exitPrice: number;
}

interface TradeRendererData {
    entry: Coordinate | number;
    exit: Coordinate | number;
    entryPrice: Coordinate | number;
    exitPrice: Coordinate | number;
    hovered: boolean;
}

interface TradeViewData {
    data: TradeRendererData[];
    options: Required<TradesIndicatorOptions>;
}

class TradesIndicatorPaneRenderer implements IPrimitivePaneRenderer {
    _viewData: TradeViewData;

    constructor(data: TradeViewData) {
        this._viewData = data;
    }

    _isOverlapping(a: [number, number], b: [number, number]): boolean {
        return !(a[1] <= b[0] || b[1] <= a[0]);
    }

    _getBandLevel(layers: [number, number][][], newRange: [number, number]): number {
        for (let i = 0, len = layers.length; i < len; i++) {
            const layer = layers[i];
            let overlaps = false;

            for (let j = 0, jLen = layer.length; j < jLen; j++) {
                if (this._isOverlapping(layer[j], newRange)) {
                    overlaps = true;
                    break;
                }
            }

            if (!overlaps) {
                layer.push(newRange);
                return i;
            }
        }

        layers.push([newRange]);
        return layers.length - 1;
    }

    draw(target: CanvasRenderingTarget2D) {
        const points: TradeRendererData[] = this._viewData.data;
        const options = this._viewData.options;
        let layers: [number, number][][] = [];

        target.useBitmapCoordinateSpace(scope => {
            const ctx = scope.context;

            // ctx.fillStyle = 'white';
            // ctx.fillStyle = 'rgba(67,63,63,0.25)';


            options.lineWidth = 1;
            ctx.lineWidth = scope.horizontalPixelRatio * options.lineWidth;

            const margin = 1;
            const bandSpacing = 3;
            let layer = 0;
            for (const point of points) {
                if (point.exit < 0)
                    continue;

                ctx.fillStyle = 'rgba(100,95,95,0.8)';
                if (!point.hovered) {
                    continue
                    ctx.fillStyle = 'rgb(18,219,156)';
                }

                const entryPosition = positionsLine(
                    point.entry,
                    scope.verticalPixelRatio,
                    options.lineWidth
                );

                const exitPosition = positionsLine(
                    point.exit,
                    scope.verticalPixelRatio,
                    options.lineWidth
                );

                const entryPricePosition = positionsLine(
                    point.entryPrice,
                    scope.horizontalPixelRatio,
                    options.lineWidth
                );

                const exitPricePosition = positionsLine(
                    point.exitPrice,
                    scope.horizontalPixelRatio,
                    options.lineWidth
                );

                layer = this._getBandLevel(layers, [point.entry, point.exit]);
                // layer++;
                ctx.strokeText(layer.toString(), entryPosition.position, entryPricePosition.position);
                const bandLength = exitPosition.position - entryPosition.position + exitPosition.length;
                const bandPosition = scope.bitmapSize.height - (margin + (layer * bandSpacing));


                if (options.showBranches) {

                    ctx.fillRect(
                        entryPosition.position,
                        entryPricePosition.position,
                        10 * scope.horizontalPixelRatio,
                        entryPosition.length,
                    );

                    ctx.fillRect(
                        exitPosition.position - 10 * scope.horizontalPixelRatio,
                        exitPricePosition.position,
                        10 * scope.horizontalPixelRatio,
                        entryPosition.length,
                    );

                    ctx.fillRect(
                        entryPosition.position,
                        entryPricePosition.position,
                        entryPosition.length,
                        bandPosition - entryPricePosition.position,
                    );


                    ctx.fillRect(
                        exitPosition.position,
                        exitPricePosition.position,
                        exitPosition.length,
                        bandPosition - exitPricePosition.position,
                    );

                }
                // ctx.fillStyle = 'rgba(63,194,242,1)';
                ctx.fillRect(
                    entryPosition.position,
                    bandPosition,
                    bandLength,
                    entryPosition.length,
                );

            }

        });
    }

    drawBackground(target: CanvasRenderingTarget2D) {
        // const points: TradeRendererData[] = this._viewData.data;
        // target.useBitmapCoordinateSpace(scope => {
        //     const ctx = scope.context;
        //     ctx.scale(scope.horizontalPixelRatio, scope.verticalPixelRatio);
        //
        //     ctx.strokeStyle = this._viewData.options.lineColor;
        //     ctx.lineWidth = this._viewData.options.lineWidth;
        //     ctx.beginPath();
        //     const region = new Path2D();
        //     const lines = new Path2D();
        //     region.moveTo(points[0].x, points[0].upper);
        //     lines.moveTo(points[0].x, points[0].upper);
        //     for (const point of points) {
        //         region.lineTo(point.x, point.upper);
        //         lines.lineTo(point.x, point.upper);
        //     }
        //     const end = points.length - 1;
        //     region.lineTo(points[end].x, points[end].lower);
        //     lines.moveTo(points[end].x, points[end].lower);
        //     for (let i = points.length - 2; i >= 0; i--) {
        //         region.lineTo(points[i].x, points[i].lower);
        //         lines.lineTo(points[i].x, points[i].lower);
        //     }
        //     region.lineTo(points[0].x, points[0].upper);
        //     region.closePath();
        //     ctx.stroke(lines);
        //     ctx.fillStyle = this._viewData.options.fillColor;
        //     ctx.fill(region);
        // });
    }
}


class TradesIndicatorPaneView implements IPrimitivePaneView {
    _source: TradesIndicator;
    _data: TradeViewData;

    constructor(source: TradesIndicator) {
        this._source = source;
        this._data = {
            data: [],
            options: this._source._options,
        };
    }

    update(hoveredTradeIndex: number = 0) {
        const series = this._source.series;
        const timeScale = this._source.chart.timeScale();
        const timeIndices = this._source._timeIndices;

        this._data.data = this._source._tradeData.map((d, index) => {
            return {
                entry: timeScale.timeToCoordinate(d.entryTime) ?? 0,
                exit: timeScale.timeToCoordinate(d.exitTime) ?? 0,
                entryPrice: series.priceToCoordinate(d.entryPrice) ?? 0,
                exitPrice: series.priceToCoordinate(d.exitPrice) ?? 0,
                hovered: hoveredTradeIndex === this._source._timeIndices.findClosestIndex(
                        d.entryTime,
                        'left'
                    ) ||
                    hoveredTradeIndex === this._source._timeIndices.findClosestIndex(
                        d.exitTime,
                        'left'
                    )
            };
        });
    }

    renderer() {
        return new TradesIndicatorPaneRenderer(this._data);
    }

}


// plugin entry point
export class TradesIndicator extends PluginBase implements ISeriesPrimitive<Time> {
    _paneViews: TradesIndicatorPaneView[];
    _seriesData: SeriesDataItemTypeMap[SeriesType][] = [];
    _tradeData: TradeData[] = [];
    _options: Required<TradesIndicatorOptions>;
    _timeIndices: ClosestTimeIndexFinder<{ time: number }>;
    _upperLower: UpperLowerInRange<any>;
    _touchChartEvents: MultiTouchChartEvents | null = null;
    _hoveredTradeIndex: number = -1;

    constructor(options: TradesIndicatorOptions = {}) {
        super();
        this._options = {...defaults, ...options};
        this._paneViews = [
            new TradesIndicatorPaneView(this)
        ];
        this._timeIndices = new ClosestTimeIndexFinder([]);
        this._upperLower = new UpperLowerInRange([]);
    }

    updateAllViews() {
        this._paneViews.forEach(pw => pw.update(this._hoveredTradeIndex));
    }

    paneViews() {
        return this._paneViews;
    }

    attached(p: SeriesAttachedParameter<Time>): void {
        super.attached(p);
        this.dataUpdated('full');

        this._touchChartEvents = new MultiTouchChartEvents(p.chart, {
            simulateMultiTouchUsingMouseDrag: true,
        });

        this._touchChartEvents
            .move()
            .subscribe((interactions: MultiTouchInteraction) => {
                this._hoveredTradeIndex = interactions.points[0].index;
                this.requestUpdate();
            }, this);
    }

    detached() {
        if (this._touchChartEvents) {
            this._touchChartEvents.move().unsubscribeAll(this);
            this._touchChartEvents.destroy();
        }
    }

    dataUpdated(scope: DataChangedScope) {
        this._seriesData = cloneReadonly(this.series.data());

        if (scope === 'full') {
            this._timeIndices = new ClosestTimeIndexFinder(
                this._seriesData as { time: number }[]
            );
        }
    }

    autoscaleInfo(startTimePoint: Logical, endTimePoint: Logical): AutoscaleInfo {

        const ts = this.chart.timeScale();

        const startTime = (ts.coordinateToTime(
            ts.logicalToCoordinate(startTimePoint) ?? 0
        ) ?? 0) as number;

        const endTime = (ts.coordinateToTime(
            ts.logicalToCoordinate(endTimePoint) ?? 5000000000
        ) ?? 5000000000) as number;

        const startIndex = this._timeIndices.findClosestIndex(startTime, 'left');
        const endIndex = this._timeIndices.findClosestIndex(endTime, 'right');
        const range = this._upperLower.getMinMax(startIndex, endIndex);
        return {
            priceRange: {
                minValue: range.lower,
                maxValue: range.upper,
            },
        };
    }

    setData(tradeData: TradeData[]) {
        // we want the shorter trades to be on top to limit the amount of crossing.

        this._tradeData = tradeData;

        // this._tradeData = tradeData.sort((a, b) =>
        //     (convertTime(b.exitTime) - convertTime(b.entryTime)) -
        //     (convertTime(a.exitTime) - convertTime(a.entryTime))
        // );
    }
}

<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - User Price Lines Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>User Price Line</h1>
			<p>
				Price lines created via the crosshair button (next to the price scale).
				The crosshair button only appears when the cursor is near the price
				scale.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>

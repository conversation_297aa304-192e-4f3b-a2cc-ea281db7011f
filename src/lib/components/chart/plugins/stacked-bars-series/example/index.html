<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - StackedBars Series Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Stacked Bars Series</h1>
			<p>
				A stacked bar plot is a graphical representation that displays
				categories as segments of a rectangular bar, stacked on top of each
				other. Each segment represents the contribution of a different variable
				to the total length of the bar, allowing for visual comparison of both
				the individual and cumulative values.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>

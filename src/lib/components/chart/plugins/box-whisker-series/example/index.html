<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - Box Whisker Series Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Box Whisker Plot</h1>
			<p>
				A chart style often used in statistics. A box and whisker plot is a
				visual representation of a data set that shows the distribution and
				spread of the data. It consists of a box that represents the
				interquartile range, with a line inside the box indicating the median,
				and "whiskers" extending from the box to show the minimum and maximum
				values. Outliers are shown as dots above and below the whiskers.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>

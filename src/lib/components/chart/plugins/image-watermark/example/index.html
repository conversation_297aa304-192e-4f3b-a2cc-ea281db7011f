<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - Image Watermark Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
		<style>
			#chart {
				height: 450px;
			}
		</style>
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Image Watermark</h1>
			<p>
				Image watermark which is responsive to the size of the chart pane.
				Padding, maximum width, maximum height values can be defined to control
				the behaviour of the watermark.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>

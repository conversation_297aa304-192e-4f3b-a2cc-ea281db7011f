<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - Delta Tooltip Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
		<style>
			#info {
				margin-left: 20px;
			}
		</style>
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Delta Tooltip</h1>
			<p class="hint-message">Hint: Use multi-touch, or click and drag.</p>
			<p>
				The Delta tooltip can be used to show the differences between two points
				on the chart. Functioning as a normal crosshair tooltip, until the user
				either uses a multi-touch gesture or clicks and drags the mouse across
				the chart.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>

<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - Session Highlighting Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Session Highlighting</h1>
			<p>
				A plugin for shading the background behind the bars on the chart
				according to a provided function. This could be used to show pre and
				post market sessions, or any other case where you would like to adjust
				the background color behind specific data points.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>

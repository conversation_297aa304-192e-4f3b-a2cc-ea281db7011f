<script lang="ts">
    import {goto} from '$app/navigation';
    import {onMount} from 'svelte';
    import Chart from "$lib/components/chart/Chart.svelte";
    import {Tabs} from '@skeletonlabs/skeleton-svelte';
    import SplitPane from '$lib/components/SplitPane.svelte';
    import SplitPanel from "$lib/components/SplitPanel.svelte";

    const tableData = [];

    let group = $state('plane');
    onMount(() => {
        goto('/dashboard');
    });
</script>

<div class="container-content-height w-full p-1">
    <SplitPane id="main" type="horizontal">
        {#snippet a()}
            <SplitPane id="sub" type="vertical">
                {#snippet a({maximize})}
                    <div class="text-2xl bg-amber-600">
                        <button onclick={maximize}>max</button>
                    </div>

                {/snippet}
                {#snippet b({maximize})}
                    <div class="text-2xl bg-amber-700">
                        <button onclick={maximize}>max</button>
                    </div>
                {/snippet}
            </SplitPane>

<!--            <SplitPane type="vertical" gap="4px">-->
<!--                {#snippet a()}-->
<!--                    <SplitPanel>-->
<!--                        <Chart/>-->
<!--                    </SplitPanel>-->
<!--                {/snippet}-->
<!--                {#snippet b()}-->
<!--                    <SplitPanel>-->
<!--                        <Tabs value={group} onValueChange={(e) => (group = e.value)}>-->
<!--                            {#snippet list()}-->
<!--                                <Tabs.Control value="plane">Positions</Tabs.Control>-->
<!--                                <Tabs.Control value="boat">Orders</Tabs.Control>-->
<!--                                <Tabs.Control value="car">???</Tabs.Control>-->
<!--                            {/snippet}-->
<!--                            {#snippet content()}-->
<!--                                <Tabs.Panel value="plane">-->
<!--                                    <div class="table-wrap">-->
<!--                                        <table class="table caption-bottom">-->
<!--                                            <caption class="pt-4">A list of trades.</caption>-->
<!--                                            <thead>-->
<!--                                            <tr>-->
<!--                                                <th>Symbol</th>-->
<!--                                                <th>Side</th>-->
<!--                                                <th>Type</th>-->
<!--                                                <th>Qty</th>-->
<!--                                            </tr>-->
<!--                                            </thead>-->
<!--                                            <tbody class="[&>tr]:hover:preset-tonal-primary">-->
<!--                                            &lt;!&ndash;{#each tableData as row}&ndash;&gt;-->
<!--                                            &lt;!&ndash;  <tr>&ndash;&gt;-->
<!--                                            &lt;!&ndash;    <td>{row.position}</td>&ndash;&gt;-->
<!--                                            &lt;!&ndash;    <td>{row.symbol}</td>&ndash;&gt;-->
<!--                                            &lt;!&ndash;    <td>{row.name}</td>&ndash;&gt;-->
<!--                                            &lt;!&ndash;    <td class="text-right">{row.atomic_no}</td>&ndash;&gt;-->
<!--                                            &lt;!&ndash;  </tr>&ndash;&gt;-->
<!--                                            &lt;!&ndash;{/each}&ndash;&gt;-->
<!--                                            </tbody>-->
<!--                                            <tfoot>-->
<!--                                            <tr>-->
<!--                                                <td colspan="3">Total</td>-->
<!--                                                <td class="text-right">{tableData.length} Elements</td>-->
<!--                                            </tr>-->
<!--                                            </tfoot>-->
<!--                                        </table>-->
<!--                                    </div>-->
<!--                                </Tabs.Panel>-->
<!--                                <Tabs.Panel value="boat">Panel 2</Tabs.Panel>-->
<!--                                <Tabs.Panel value="car">Panel 3</Tabs.Panel>-->
<!--                            {/snippet}-->
<!--                        </Tabs>-->
<!--                    </SplitPanel>-->
<!--                {/snippet}-->
<!--            </SplitPane>-->
        {/snippet}
        {#snippet b({maximize})}
            <div class="text-2xl bg-amber-500 w-48">
                <button onclick={maximize}>max b</button>

            </div>
        {/snippet}
    </SplitPane>

</div>
<script lang="ts">
  import '../app.css';
  import Header from '$lib/components/Header.svelte';

  const { children } = $props();
</script>

<div class="grid min-h-screen grid-rows-[auto_1fr_auto]">
  <!-- Header -->
  <div class="sticky top-0 z-50 w-full overflow-hidden shadow">
    <Header />
  </div>

  <!-- Main -->
  <main class="w-full mx-auto overflow-y-auto">
    {@render children()}
  </main>

  <!-- Footer -->
<!--  <footer class="p-4">(footer)</footer>-->
</div>
